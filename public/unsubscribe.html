<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribed - CaseBuilder</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', Arial, sans-serif;
            background-color: #121212;
            color: #FFFFFF;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background-color: #FFFFFF;
            color: #333333;
            max-width: 500px;
            width: 90%;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #222222;
        }

        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            color: #555555;
        }

        .logo {
            margin-bottom: 30px;
            max-width: 200px;
            height: auto;
        }

        .icon {
            width: 60px;
            height: 60px;
            margin-bottom: 20px;
            color: #00BFD4;
        }

        .footer {
            margin-top: 30px;
            font-size: 14px;
            color: #777777;
        }

        a {
            color: #00BFD4;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="images/logo.svg" alt="CaseBuilder Logo" class="logo">

        <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>

        <h1>You've been unsubscribed</h1>

        <p>You will no longer receive email updates from CaseBuilderAI. If this was a mistake, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

        <div class="footer">
            &copy; 2024 CaseBuilderAI, LLC. All rights reserved.
        </div>
    </div>

    <script>
        // Extract email parameter from URL
        function getEmailFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('email');
        }

        // Send unsubscribe request to Google Script
        function sendUnsubscribeRequest() {
            const email = getEmailFromUrl();

            if (email) {
                try {
                    // Send the request silently
                    fetch("https://script.google.com/macros/s/AKfycbzCNQ0BseMuH7zNK86p6pyhgY3a9TCv39U6GUxkWLvLANJTZtpQLt52Cc0Ccc14g/exec?email=" + encodeURIComponent(email))
                    .catch(error => {
                        // Silently handle any errors
                        console.error('Error sending unsubscribe request:', error);
                    });
                } catch (error) {
                    // Silently handle any errors
                    console.error('Error in unsubscribe process:', error);
                }
            }
        }

        // Execute the unsubscribe request when the page loads
        document.addEventListener('DOMContentLoaded', sendUnsubscribeRequest);
    </script>
</body>
</html>
